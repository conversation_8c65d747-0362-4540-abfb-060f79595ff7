import sys
import uuid
import traceback
from typing import Dict, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import all existing modules
from config import ConfigManager
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM,
    OllamaLLM
)
from workflow import GraphBuilder

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="QMigrator AI - Database Migration API",
    description="AI-powered database code conversion with real-time migration workflow",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure as needed for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize configuration to get database names
try:
    config_manager = ConfigManager()
    config_manager.load_from_env()
    source_db = config_manager.get_source_database()
    target_db = config_manager.get_target_database()
    migration_direction = config_manager.get_migration_direction()
except Exception as e:
    print(f"Configuration Error: {str(e)}")
    print("Please ensure SOURCE_DATABASE and TARGET_DATABASE environment variables are set in your .env file")
    sys.exit(1)


# Request and Response Models
class MigrationRequest(BaseModel):
    """
    Request model for database migration endpoint.
    
    This model defines the input parameters required for the AI-driven database
    migration workflow, including source code, target code with errors, and
    deployment error messages.
    
    Attributes:
        source_code: Original source database code to be migrated
        target_code: Target database code with potential deployment errors  
        deployment_error: Error message from target database deployment attempt
    """
    source_code: str = Field(
        ..., 
        description=f"Original {source_db} source code to be migrated",
        min_length=1
    )
    target_code: str = Field(
        ..., 
        description=f"{target_db} target code with potential deployment errors",
        min_length=1
    )
    deployment_error: str = Field(
        ..., 
        description=f"Error message from {target_db} deployment attempt",
        min_length=1
    )


class MigrationResponse(BaseModel):
    """
    Response model for database migration endpoint.
    
    This model defines the comprehensive output from the AI-driven migration
    workflow, including execution status, iteration details, and complete
    audit trail information.
    
    Attributes:
        success: Boolean indicating overall migration workflow success
        deployment_successful: Boolean indicating target database deployment success
        iteration_count: Number of iterations performed during migration
        migration_direction: Database migration direction (e.g., "Oracle to PostgreSQL")
        workflow_results: Complete workflow execution results and state
        error_message: Error message if migration failed
        thread_id: Unique thread identifier for workflow execution tracking
    """
    success: bool = Field(description="Overall migration workflow success status")
    deployment_successful: Optional[bool] = Field(description="Target database deployment success status")
    iteration_count: Optional[int] = Field(description="Number of iterations performed during migration")
    migration_direction: str = Field(description="Database migration direction")
    workflow_results: Optional[Dict[str, Any]] = Field(description="Complete workflow execution results and state")
    error_message: Optional[str] = Field(description="Error message if migration failed")
    thread_id: str = Field(description="Unique thread identifier for workflow execution tracking")


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """
    Create and initialize a Language Model instance based on the specified provider.

    This function creates LLM instances for database migration workflows.
    It supports multiple AI providers to ensure flexibility and reliability in the conversion process.
    Each provider is configured with appropriate settings for optimal SQL conversion performance.

    Supported Providers:
        - azure_openai: Microsoft Azure OpenAI service with enterprise-grade security
        - openai: OpenAI's GPT models for advanced SQL understanding
        - anthropic: Anthropic's Claude models for precise code analysis
        - groq: Groq's high-performance inference for fast processing
        - gemini: Google's Gemini models for comprehensive language understanding
        - ollama: Local LLM deployment for privacy-sensitive environments

    Args:
        provider (str): The LLM provider identifier from supported list
        config_manager (ConfigManager): Configuration manager with provider-specific settings

    Returns:
        Any: Initialized LLM instance ready for database migration tasks

    Raises:
        ValueError: If the provider is not supported or configuration is invalid

    Example:
        >>> config = ConfigManager()
        >>> llm = create_llm("azure_openai", config)
        >>> # LLM ready for SQL conversion tasks
    """
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def setup_application() -> Any:
    """
    Set up the QMigrator AI application with configuration and initialize the Language Model.

    This function handles the complete application initialization process for database
    migration workflows. It loads configuration settings from environment variables,
    validates the LLM provider selection, and initializes the appropriate AI client for
    SQL conversion tasks.

    The setup process includes:
        - Loading configuration from environment variables
        - Validating LLM provider availability
        - Initializing the selected AI provider with proper credentials
        - Preparing the LLM for database migration operations

    Returns:
        Any: Initialized LLM instance ready for database migration workflows

    Raises:
        Exception: If LLM initialization fails due to invalid credentials, network issues,
                  or unsupported provider configuration

    Example:
        >>> llm = setup_application()
        >>> # LLM is now ready for database migration tasks
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM for database migration...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client for SQL conversion")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_workflow(llm: Any, source_code: str, target_code: str, deployment_error: str) -> Dict[str, Any]:
    """
    Execute the complete database migration workflow.

    This function orchestrates the entire AI-driven migration process using a sophisticated
    workflow that includes error analysis, source mapping, statement conversion, and validation.
    The workflow uses LangGraph for state management and supports iterative improvements
    until successful target database deployment.

    Workflow Steps:
        1. Split SQL statements for granular analysis
        2. Identify problematic target statements using hybrid AI/position-based approach
        3. Map target statements to corresponding source statements
        4. Convert statements using AI with source context
        5. Validate conversions and deploy to target database
        6. Iterate until successful deployment

    Args:
        llm (Any): Initialized Language Model instance for AI-driven SQL analysis
        source_code (str): Original source database code to be migrated
        target_code (str): Target database code with potential deployment errors
        deployment_error (str): Error message from target database deployment attempt

    Returns:
        Dict[str, Any]: Workflow execution results containing final state, iteration count,
                       deployment status, and complete audit trail

    Example:
        >>> llm = setup_application()
        >>> result = run_workflow(llm, source_sql, target_sql, error_msg)
        >>> print(f"Migration completed in {result['iteration_count']} iterations")
    """
    # Initialize the workflow graph builder with the LLM
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()

    # Generate workflow visualization for debugging and documentation
    graph_builder.save_graph_image(graph_builder.graph)

    # Create a unique thread ID for this workflow execution to enable state tracking
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"🔗 Using thread ID: {thread_id}")

    # Execute the complete migration workflow with initial state
    result = graph_builder.invoke_graph({
        "source_code": source_code,
        "target_code": target_code,
        "deployment_error": deployment_error,
        "iteration_count": 1  # Initialize iteration count for tracking
    }, thread_id=thread_id)
    
    # Add thread_id to result for tracking
    result["thread_id"] = thread_id
    return result


@app.get("/", tags=["Health"])
async def root():
    """
    Root endpoint providing API information and health status.
    
    Returns basic information about the QMigrator AI API including
    supported migration direction and available endpoints.
    """
    return {
        "message": "QMigrator AI - Database Migration API",
        "migration_direction": migration_direction,
        "source_database": source_db,
        "target_database": target_db,
        "version": "1.0.0",
        "status": "healthy",
        "endpoints": {
            "migration": "/migrate",
            "docs": "/docs",
            "redoc": "/redoc"
        }
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """
    Health check endpoint for monitoring and load balancer integration.

    Provides detailed health status including configuration validation
    and LLM provider availability.
    """
    try:
        # Test configuration
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()

        return {
            "status": "healthy",
            "migration_direction": migration_direction,
            "llm_provider": llm_provider,
            "source_database": source_db,
            "target_database": target_db,
            "timestamp": str(uuid.uuid4())
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unhealthy: {str(e)}"
        )


@app.post("/migrate", response_model=MigrationResponse, tags=["Migration"])
async def migrate_database(request: MigrationRequest):
    """
    Execute complete database migration workflow.

    This endpoint orchestrates the entire AI-driven migration process using a sophisticated
    workflow that includes error analysis, source mapping, statement conversion, and validation.
    The workflow uses LangGraph for state management and supports iterative improvements
    until successful target database deployment.

    The migration process includes:
        1. Split SQL statements for granular analysis
        2. Identify problematic target statements using hybrid AI/position-based approach
        3. Map target statements to corresponding source statements
        4. Convert statements using AI with source context
        5. Validate conversions and deploy to target database
        6. Iterate until successful deployment

    Args:
        request (MigrationRequest): Migration request containing source code, target code, and deployment error

    Returns:
        MigrationResponse: Comprehensive migration results including status, iteration count, and audit trail

    Raises:
        HTTPException: If migration setup fails or unexpected errors occur during workflow execution

    Example:
        ```python
        import requests

        response = requests.post("http://localhost:8000/migrate", json={
            "source_code": "CREATE OR REPLACE PROCEDURE...",
            "target_code": "CREATE OR REPLACE PROCEDURE...",
            "deployment_error": "ERROR: syntax error at..."
        })

        result = response.json()
        print(f"Migration successful: {result['success']}")
        print(f"Iterations: {result['iteration_count']}")
        ```
    """
    thread_id = f"thread_{uuid.uuid4()}"

    try:
        print(f"🚀 Starting migration workflow with thread ID: {thread_id}")
        print(f"📊 Migration Direction: {migration_direction}")

        # Initialize the LLM
        llm = setup_application()
        print(f"✅ LLM initialized successfully")

        # Execute the complete migration workflow
        print(f"🔄 Executing migration workflow...")
        workflow_results = run_workflow(
            llm=llm,
            source_code=request.source_code,
            target_code=request.target_code,
            deployment_error=request.deployment_error
        )

        print(f"✅ Workflow execution completed")

        # Extract key results
        deployment_successful = workflow_results.get('deployment_successful', False)
        iteration_count = workflow_results.get('iteration_count', 1)

        # Determine overall success
        success = deployment_successful is True

        print(f"📋 Migration Results:")
        print(f"   - Success: {success}")
        print(f"   - Deployment Successful: {deployment_successful}")
        print(f"   - Iterations: {iteration_count}")
        print(f"   - Thread ID: {thread_id}")

        return MigrationResponse(
            success=success,
            deployment_successful=deployment_successful,
            iteration_count=iteration_count,
            migration_direction=migration_direction,
            workflow_results=workflow_results,
            error_message=None,
            thread_id=thread_id
        )

    except ValueError as e:
        error_msg = f"Configuration Error: {str(e)}"
        print(f"❌ {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except Exception as e:
        error_msg = f"Migration workflow failed: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"🔍 Traceback: {traceback.format_exc()}")

        return MigrationResponse(
            success=False,
            deployment_successful=False,
            iteration_count=None,
            migration_direction=migration_direction,
            workflow_results=None,
            error_message=error_msg,
            thread_id=thread_id
        )


if __name__ == "__main__":
    import uvicorn

    print(f"🚀 Starting QMigrator AI FastAPI Server")
    print(f"📊 Migration Direction: {migration_direction}")
    print(f"🔵 Source Database: {source_db}")
    print(f"🟢 Target Database: {target_db}")
    print(f"🌐 Server will be available at: http://localhost:8000")
    print(f"📚 API Documentation: http://localhost:8000/docs")
    print(f"📖 ReDoc Documentation: http://localhost:8000/redoc")

    uvicorn.run(
        "fastapi_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
