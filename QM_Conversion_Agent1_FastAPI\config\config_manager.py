"""
Configuration manager for QMigrator AI database migration system.

This module provides centralized configuration management for the entire migration workflow,
including LLM provider settings, model configurations, database names, and environment-based customization.
"""

import os
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from config.model_config import (
    ModelConfig,
    default_model_config,
    AzureOpenAIConfig,
    OpenAIConfig,
    AnthropicConfig,
    GroqConfig,
    OllamaConfig,
    GeminiConfig
)


class ConfigManager(BaseModel):
    """
    Centralized configuration manager for QMigrator AI database migration system.

    This class provides comprehensive configuration management for the entire migration workflow,
    including LLM provider settings, model configurations, and environment-based customization.
    It uses Pydantic for type safety and validation, ensuring reliable configuration handling
    across all system components.

    Key Features:
        - Multi-provider LLM configuration support
        - Environment variable integration for secure credential management
        - Type-safe configuration with Pydantic validation
        - Default configuration fallbacks for development environments
        - Centralized configuration access for all workflow components

    Supported LLM Providers:
        - azure_openai: Microsoft Azure OpenAI service with enterprise security
        - openai: OpenAI's GPT models for advanced SQL understanding
        - anthropic: Anthropic's Claude models for precise code analysis
        - groq: Groq's high-performance inference for fast processing
        - gemini: Google's Gemini models for comprehensive language understanding
        - ollama: Local LLM deployment for privacy-sensitive environments

    Environment Variables:
        LLM_PROVIDER: Primary LLM provider selection
        SOURCE_DATABASE: Source database name (required)
        TARGET_DATABASE: Target database name (required)
        Provider-specific variables: API keys, endpoints, model names, etc.
    """

    # Database configuration (required from environment variables)
    source_database: str = Field(
        default="",
        description="Source database name for migration (required via SOURCE_DATABASE env var)"
    )

    target_database: str = Field(
        default="",
        description="Target database name for migration (required via TARGET_DATABASE env var)"
    )

    # Primary LLM provider selection
    provider: str = Field(
        default="azure_openai",
        description="Primary LLM provider for database migration (azure_openai, openai, anthropic, groq, gemini, ollama)"
    )

    # Comprehensive model configuration for all providers
    models_config: ModelConfig = Field(
        default_factory=lambda: default_model_config,
        description="Complete model configuration for all supported LLM providers with environment integration"
    )


    def __init__(self, **data):
        super().__init__(**data)
        self.load_from_env()

    def get_llm_provider(self) -> str:
        """Get the LLM provider (legacy method)."""
        return self.provider

    def set_llm_provider(self, provider: str):
        """Set the LLM provider (legacy method)."""
        self.provider = provider

    def get_source_database(self) -> str:
        """Get the source database name."""
        return self.source_database

    def get_target_database(self) -> str:
        """Get the target database name."""
        return self.target_database

    def get_migration_direction(self) -> str:
        """Get the migration direction as 'Source to Target'."""
        return f"{self.source_database} to {self.target_database}"

    def get_llm_config(self):
        """Get LLM configuration as a dictionary."""
        if self.provider == "azure_openai":
            return self.models_config.azure_openai.model_dump()
        elif self.provider == "openai":
            return self.models_config.openai.model_dump()
        elif self.provider == "anthropic":
            return self.models_config.anthropic.model_dump()
        elif self.provider == "groq":
            return self.models_config.groq.model_dump()
        elif self.provider == "ollama":
            return self.models_config.ollama.model_dump()
        elif self.provider == "gemini":
            return self.models_config.gemini.model_dump()
        else:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")

    def load_from_env(self):
        """Load configuration from environment variables."""
        # Ensure .env file is loaded
        load_dotenv()

        # Load provider from environment
        provider = os.getenv('LLM_PROVIDER')
        if provider:
            self.provider = provider

        # Load database names from environment (required)
        source_db = os.getenv('SOURCE_DATABASE')
        if not source_db:
            raise ValueError("SOURCE_DATABASE environment variable is required")
        self.source_database = source_db

        target_db = os.getenv('TARGET_DATABASE')
        if not target_db:
            raise ValueError("TARGET_DATABASE environment variable is required")
        self.target_database = target_db


        # Since we're now loading all values directly in the model_config.py file,
        # we don't need to manually set each field here.
        # We just need to create new instances of the config classes to ensure
        # they load their values from environment variables.

        # Load Azure OpenAI configuration
        if self.provider == 'azure_openai':
            self.models_config.azure_openai = AzureOpenAIConfig()

        # Load OpenAI configuration
        elif self.provider == 'openai':
            self.models_config.openai = OpenAIConfig()

        # Load Anthropic configuration
        elif self.provider == 'anthropic':
            self.models_config.anthropic = AnthropicConfig()

        # Load Groq configuration
        elif self.provider == 'groq':
            self.models_config.groq = GroqConfig()

        # Load Gemini configuration
        elif self.provider == 'gemini':
            self.models_config.gemini = GeminiConfig()

        # Load Ollama configuration
        elif self.provider == 'ollama':
            self.models_config.ollama = OllamaConfig()
