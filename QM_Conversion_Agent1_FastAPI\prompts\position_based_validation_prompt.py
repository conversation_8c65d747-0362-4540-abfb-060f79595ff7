"""
Prompts for position-based error identification validation in database conversion.
"""
from typing import Dict
from utils.database_names import get_database_specific_terms

def create_position_based_validation_prompt(target_error_context: Dict, error_message: str) -> str:
    """
    Creates an ultra-simple validation prompt: Does this statement cause the deployment error?

    This function creates a prompt that asks ONE simple question:
    "Would running this statement in the target database produce this exact deployment error?"

    Args:
        target_error_context: Dictionary containing the error context (before, error, after statements)
        error_message: The error message from deployment

    Returns:
        A formatted prompt string asking only if this statement causes the deployment error
    """
    # Get dynamic database names
    db_terms = get_database_specific_terms()
    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']
    migration_direction = db_terms['migration_direction']

    return f"""You are a {expert_title} with deep expertise in both {source_db} and {target_db} database systems. Your task is to validate if the position-based identified statement is causing the reported error during {migration_direction} migration.

ERROR MESSAGE:
{error_message}

POSITION-BASED IDENTIFIED CONTEXT:
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

SIMPLE QUESTION:
Does this statement cause the deployment error? YES or NO?

ONLY CHECK:
- Would running this statement in {target_db} produce this exact deployment error?

THAT'S IT. Nothing else.

OUTPUT FORMAT (JSON):
{{
  "is_correct": true/false,
  "confidence": <float between 0-1>,
  "explanation": "<one sentence: does this statement cause this deployment error? yes/no and why>"
}}

FOCUS: Only answer - does this statement cause the deployment error?"""
