from langchain_ollama import ChatOllama
from config import ConfigManager


class OllamaLLM:
    """Ollama LLM implementation using LangChain."""

    def __init__(self, config_manager: ConfigManager):
        """Initialize the Ollama LLM with configuration.

        Args:
            config_manager: Configuration manager containing Ollama settings
        """
        self.config_manager = config_manager
        ollama_config = config_manager.models_config.ollama
        self.base_url = ollama_config.base_url
        self.model_name = ollama_config.model_name

        # Initialize the Ollama client
        self.client = ChatOllama(
            base_url=self.base_url,
            model=self.model_name,
            temperature=ollama_config.temperature,
            num_predict=ollama_config.max_tokens,
        )
        print(f"Successfully initialized Ollama client with model {self.model_name}")