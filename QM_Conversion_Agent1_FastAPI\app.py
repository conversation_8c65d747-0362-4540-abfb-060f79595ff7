import sys
import uuid
import traceback
from typing import Dict, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import all existing modules - all imports at the top
from config import ConfigManager
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM,
    OllamaLLM
)
from workflow import GraphBuilder

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="QMigrator AI - Database Migration API",
    description="AI-powered database code conversion with real-time migration workflow",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure as needed for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize configuration to get database names
try:
    config_manager = ConfigManager()
    config_manager.load_from_env()
    source_db = config_manager.get_source_database()
    target_db = config_manager.get_target_database()
    migration_direction = config_manager.get_migration_direction()
except Exception as e:
    print(f"Configuration Error: {str(e)}")
    print("Please ensure SOURCE_DATABASE and TARGET_DATABASE environment variables are set in your .env file")
    sys.exit(1)


# Request and Response Models
class MigrationRequest(BaseModel):
    """
    Request model for database migration endpoint.
    
    This model defines the input parameters required for the AI-driven database
    migration workflow, including source code, target code with errors, and
    deployment error messages.
    
    Attributes:
        source_code: Original source database code to be migrated
        target_code: Target database code with potential deployment errors  
        deployment_error: Error message from target database deployment attempt
    """
    source_code: str = Field(
        ..., 
        description=f"Original {source_db} source code to be migrated",
        min_length=1
    )
    target_code: str = Field(
        ..., 
        description=f"{target_db} target code with potential deployment errors",
        min_length=1
    )
    deployment_error: str = Field(
        ..., 
        description=f"Error message from {target_db} deployment attempt",
        min_length=1
    )


class MigrationResponse(BaseModel):
    """
    Response model for database migration endpoint.
    
    This model defines the comprehensive output from the AI-driven migration
    workflow, including execution status, iteration details, and complete
    audit trail information.
    
    Attributes:
        success: Boolean indicating overall migration workflow success
        deployment_successful: Boolean indicating target database deployment success
        iteration_count: Number of iterations performed during migration
        migration_direction: Database migration direction (e.g., "Oracle to PostgreSQL")
        workflow_results: Complete workflow execution results and state
        error_message: Error message if migration failed
        thread_id: Unique thread identifier for workflow execution tracking
    """
    success: bool = Field(description="Overall migration workflow success status")
    deployment_successful: Optional[bool] = Field(description="Target database deployment success status")
    iteration_count: Optional[int] = Field(description="Number of iterations performed during migration")
    migration_direction: str = Field(description="Database migration direction")
    workflow_results: Optional[Dict[str, Any]] = Field(description="Complete workflow execution results and state")
    error_message: Optional[str] = Field(description="Error message if migration failed")
    thread_id: str = Field(description="Unique thread identifier for workflow execution tracking")


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """
    Create and initialize a Language Model instance based on the specified provider.

    This function creates LLM instances for database migration workflows.
    It supports multiple AI providers to ensure flexibility and reliability in the conversion process.
    Each provider is configured with appropriate settings for optimal SQL conversion performance.

    Supported Providers:
        - azure_openai: Microsoft Azure OpenAI service with enterprise-grade security
        - openai: OpenAI's GPT models for advanced SQL understanding
        - anthropic: Anthropic's Claude models for precise code analysis
        - groq: Groq's high-performance inference for fast processing
        - gemini: Google's Gemini models for comprehensive language understanding
        - ollama: Local LLM deployment for privacy-sensitive environments

    Args:
        provider (str): The LLM provider identifier from supported list
        config_manager (ConfigManager): Configuration manager with provider-specific settings

    Returns:
        Any: Initialized LLM instance ready for database migration tasks

    Raises:
        ValueError: If the provider is not supported or configuration is invalid

    Example:
        >>> config = ConfigManager()
        >>> llm = create_llm("azure_openai", config)
        >>> # LLM ready for SQL conversion tasks
    """
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def setup_application() -> Any:
    """
    Set up the QMigrator AI application with configuration and initialize the Language Model.

    This function handles the complete application initialization process for database
    migration workflows. It loads configuration settings from environment variables,
    validates the LLM provider selection, and initializes the appropriate AI client for
    SQL conversion tasks.

    The setup process includes:
        - Loading configuration from environment variables
        - Validating LLM provider availability
        - Initializing the selected AI provider with proper credentials
        - Preparing the LLM for database migration operations

    Returns:
        Any: Initialized LLM instance ready for database migration workflows

    Raises:
        Exception: If LLM initialization fails due to invalid credentials, network issues,
                  or unsupported provider configuration

    Example:
        >>> llm = setup_application()
        >>> # LLM is now ready for database migration tasks
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM for database migration...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client for SQL conversion")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_workflow(llm: Any, source_code: str, target_code: str, deployment_error: str) -> Dict[str, Any]:
    """
    Execute the complete database migration workflow.

    This function orchestrates the entire AI-driven migration process using a sophisticated
    workflow that includes error analysis, source mapping, statement conversion, and validation.
    The workflow uses LangGraph for state management and supports iterative improvements
    until successful target database deployment.

    Workflow Steps:
        1. Split SQL statements for granular analysis
        2. Identify problematic target statements using hybrid AI/position-based approach
        3. Map target statements to corresponding source statements
        4. Convert statements using AI with source context
        5. Validate conversions and deploy to target database
        6. Iterate until successful deployment

    Args:
        llm (Any): Initialized Language Model instance for AI-driven SQL analysis
        source_code (str): Original source database code to be migrated
        target_code (str): Target database code with potential deployment errors
        deployment_error (str): Error message from target database deployment attempt

    Returns:
        Dict[str, Any]: Workflow execution results containing final state, iteration count,
                       deployment status, and complete audit trail

    Example:
        >>> llm = setup_application()
        >>> result = run_workflow(llm, source_sql, target_sql, error_msg)
        >>> print(f"Migration completed in {result['iteration_count']} iterations")
    """
    # Initialize the workflow graph builder with the LLM
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()

    # Generate workflow visualization for debugging and documentation
    graph_builder.save_graph_image(graph_builder.graph)

    # Create a unique thread ID for this workflow execution to enable state tracking
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"🔗 Using thread ID: {thread_id}")

    # Execute the complete migration workflow with initial state
    result = graph_builder.invoke_graph({
        "source_code": source_code,
        "target_code": target_code,
        "deployment_error": deployment_error,
        "iteration_count": 1  # Initialize iteration count for tracking
    }, thread_id=thread_id)
    
    # Add thread_id to result for tracking
    result["thread_id"] = thread_id
    return result
